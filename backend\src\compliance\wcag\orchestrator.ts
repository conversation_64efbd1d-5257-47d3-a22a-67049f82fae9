/**
 * WCAG Compliance Orchestrator
 * Coordinates all WCAG compliance checks and manages scan execution
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import puppeteerEx<PERSON> from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import {
  WcagScanModel,
  WcagAutomatedResultModel,
  ContrastAnalysisResult,
  FocusAnalysisResult,
  KeyboardAnalysisResult,
  WcagRuleId,
  ScanStatus
} from './types';
import { ColorAnalyzer } from './utils/color-analyzer';
import { FocusTracker } from './utils/focus-tracker';
import { KeyboardTester } from './utils/keyboard-tester';
import { LayoutAnalyzer } from './utils/layout-analyzer';
import { WCAG_AUTOMATED_RULES } from './constants';
import {
  ContrastMinimumCheck,
  FocusVisibleCheck,
  FocusNotObscuredMinimumCheck,
  FocusNotObscuredEnhancedCheck,
  FocusAppearance<PERSON>heck,
  TargetSizeChe<PERSON>,
  NonTextContent<PERSON>heck,
  InfoRelationships<PERSON>heck
} from './checks';

// Configure Puppeteer with stealth plugin
puppeteerExtra.use(StealthPlugin());

export interface ScanConfiguration {
  url: string;
  waitForSelector?: string;
  timeout?: number;
  viewport?: {
    width: number;
    height: number;
  };
  userAgent?: string;
  enableJavaScript?: boolean;
  enableImages?: boolean;
}

export interface ScanProgress {
  scanId: string;
  status: ScanStatus;
  currentRule?: WcagRuleId;
  completedRules: WcagRuleId[];
  totalRules: number;
  progress: number;
  estimatedTimeRemaining?: number;
}

export class WcagOrchestrator {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private scanStartTime: Date | null = null;

  /**
   * Initialize the orchestrator with browser setup
   */
  async initialize(): Promise<void> {
    try {
      this.browser = await puppeteerExtra.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });
      
      this.page = await this.browser.newPage();
      
      // Set default viewport
      await this.page.setViewport({
        width: 1920,
        height: 1080,
        deviceScaleFactor: 1
      });
      
      // Set user agent
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );
      
    } catch (error) {
      console.error('Failed to initialize WCAG orchestrator:', error);
      throw new Error('Orchestrator initialization failed');
    }
  }

  /**
   * Start a comprehensive WCAG compliance scan
   */
  async startScan(config: ScanConfiguration): Promise<string> {
    if (!this.browser || !this.page) {
      throw new Error('Orchestrator not initialized. Call initialize() first.');
    }

    this.scanStartTime = new Date();
    const scanId = this.generateScanId();
    
    try {
      // Create initial scan record
      const scan: Partial<WcagScanModel> = {
        id: scanId,
        target_url: config.url,
        scan_status: 'running',
        scan_timestamp: this.scanStartTime,
        total_automated_checks: WCAG_AUTOMATED_RULES.length,
        passed_automated_checks: 0,
        failed_automated_checks: 0,
        manual_review_items: 0
      };
      
      // TODO: Save scan to database
      console.log('Starting WCAG scan:', scan);
      
      // Configure page based on scan configuration
      await this.configurePage(config);
      
      // Navigate to target URL
      await this.page.goto(config.url, {
        waitUntil: 'networkidle2',
        timeout: config.timeout || 30000
      });
      
      // Wait for specific selector if provided
      if (config.waitForSelector) {
        await this.page.waitForSelector(config.waitForSelector, {
          timeout: config.timeout || 10000
        });
      }
      
      // Start automated checks
      await this.runAutomatedChecks(scanId);
      
      return scanId;
      
    } catch (error) {
      console.error('Error starting WCAG scan:', error);
      
      // Update scan status to failed
      // TODO: Update scan in database
      
      throw new Error(`Scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Configure page settings based on scan configuration
   */
  private async configurePage(config: ScanConfiguration): Promise<void> {
    if (!this.page) return;
    
    // Set viewport if specified
    if (config.viewport) {
      await this.page.setViewport({
        width: config.viewport.width,
        height: config.viewport.height,
        deviceScaleFactor: 1
      });
    }
    
    // Set user agent if specified
    if (config.userAgent) {
      await this.page.setUserAgent(config.userAgent);
    }
    
    // Configure JavaScript
    await this.page.setJavaScriptEnabled(config.enableJavaScript !== false);
    
    // Configure images
    if (config.enableImages === false) {
      await this.page.setRequestInterception(true);
      this.page.on('request', (request) => {
        if (request.resourceType() === 'image') {
          request.abort();
        } else {
          request.continue();
        }
      });
    }
  }

  /**
   * Run all automated WCAG checks
   */
  private async runAutomatedChecks(scanId: string): Promise<void> {
    if (!this.page) return;

    const automatedRules = WCAG_AUTOMATED_RULES;

    console.log(`Running ${automatedRules.length} automated checks...`);

    // Run color contrast analysis
    await this.runContrastAnalysis(scanId);

    // Run focus analysis
    await this.runFocusAnalysis(scanId);

    // Run keyboard analysis
    await this.runKeyboardAnalysis(scanId);

    // Run layout analysis
    await this.runLayoutAnalysis(scanId);

    // Run Part 3 fully automated checks
    await this.runPart3AutomatedChecks(scanId);

    console.log('Automated checks completed');
  }

  /**
   * Run color contrast analysis
   */
  private async runContrastAnalysis(scanId: string): Promise<void> {
    if (!this.page) return;
    
    try {
      console.log('Running contrast analysis...');
      
      // Get all text elements with their colors
      const textElements = await this.page.evaluate(() => {
        const elements: Array<{
          selector: string;
          text: string;
          color: string;
          backgroundColor: string;
          fontSize: string;
          fontWeight: string;
        }> = [];
        
        const textNodes = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div, a, button, label');
        
        textNodes.forEach((element, index) => {
          const computedStyle = window.getComputedStyle(element);
          const text = element.textContent?.trim();
          
          if (text && text.length > 0) {
            elements.push({
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              text: text.substring(0, 100), // Limit text length
              color: computedStyle.color,
              backgroundColor: computedStyle.backgroundColor,
              fontSize: computedStyle.fontSize,
              fontWeight: computedStyle.fontWeight
            });
          }
        });
        
        return elements.slice(0, 50); // Limit to first 50 elements
      });
      
      // Analyze each text element
      for (const element of textElements) {
        const isLargeText = ColorAnalyzer.isLargeText(element.fontSize, element.fontWeight);
        const contrastResult = ColorAnalyzer.analyzeContrast(
          element.color,
          element.backgroundColor,
          isLargeText
        );
        
        // TODO: Save contrast analysis to database
        const analysis: ContrastAnalysisResult = {
          elementSelector: element.selector,
          elementType: 'text',
          foregroundColor: element.color,
          backgroundColor: element.backgroundColor,
          contrastRatio: contrastResult.ratio,
          isLargeText: isLargeText,
          levelAAPass: contrastResult.level !== 'FAIL',
          levelAAAPass: contrastResult.level === 'AAA',
          contextDescription: `Text element with ${element.text.substring(0, 50)}...`,
          recommendation: contrastResult.recommendation || 'No issues found'
        };
        
        console.log('Contrast analysis:', analysis);
      }
      
    } catch (error) {
      console.error('Error in contrast analysis:', error);
    }
  }

  /**
   * Run focus analysis
   */
  private async runFocusAnalysis(scanId: string): Promise<void> {
    if (!this.page) return;
    
    try {
      console.log('Running focus analysis...');
      
      const focusReport = await FocusTracker.generateFocusReport(this.page);
      
      // TODO: Save focus analysis to database
      console.log('Focus analysis:', focusReport);
      
    } catch (error) {
      console.error('Error in focus analysis:', error);
    }
  }

  /**
   * Run keyboard analysis
   */
  private async runKeyboardAnalysis(scanId: string): Promise<void> {
    if (!this.page) return;
    
    try {
      console.log('Running keyboard analysis...');
      
      const keyboardReport = await KeyboardTester.generateKeyboardReport(this.page);
      
      // TODO: Save keyboard analysis to database
      console.log('Keyboard analysis:', keyboardReport);
      
    } catch (error) {
      console.error('Error in keyboard analysis:', error);
    }
  }

  /**
   * Run layout analysis
   */
  private async runLayoutAnalysis(scanId: string): Promise<void> {
    if (!this.page) return;
    
    try {
      console.log('Running layout analysis...');
      
      const layoutReport = await LayoutAnalyzer.analyzePageLayout(this.page);
      
      // TODO: Save layout analysis to database
      console.log('Layout analysis:', layoutReport);
      
    } catch (error) {
      console.error('Error in layout analysis:', error);
    }
  }

  /**
   * Run Part 3 fully automated checks
   */
  private async runPart3AutomatedChecks(scanId: string): Promise<void> {
    if (!this.page) return;

    console.log('🚀 Running Part 3 fully automated checks...');

    const checkConfig = {
      targetUrl: await this.page.url(),
      timeout: 30000,
      scanId,
      page: this.page
    };

    try {
      // Rule 4: Contrast Minimum
      console.log('Running Contrast Minimum check...');
      const contrastCheck = new ContrastMinimumCheck();
      const contrastResult = await contrastCheck.performCheck(checkConfig);
      console.log(`Contrast check completed: ${contrastResult.status}`);

      // Rule 7: Focus Visible
      console.log('Running Focus Visible check...');
      const focusVisibleCheck = new FocusVisibleCheck();
      const focusVisibleResult = await focusVisibleCheck.performCheck(checkConfig);
      console.log(`Focus Visible check completed: ${focusVisibleResult.status}`);

      // Rule 10: Focus Not Obscured Minimum
      console.log('Running Focus Not Obscured Minimum check...');
      const focusNotObscuredMinCheck = new FocusNotObscuredMinimumCheck();
      const focusNotObscuredMinResult = await focusNotObscuredMinCheck.performCheck(checkConfig);
      console.log(`Focus Not Obscured Minimum check completed: ${focusNotObscuredMinResult.status}`);

      // Rule 11: Focus Not Obscured Enhanced
      console.log('Running Focus Not Obscured Enhanced check...');
      const focusNotObscuredEnhCheck = new FocusNotObscuredEnhancedCheck();
      const focusNotObscuredEnhResult = await focusNotObscuredEnhCheck.performCheck(checkConfig);
      console.log(`Focus Not Obscured Enhanced check completed: ${focusNotObscuredEnhResult.status}`);

      // Rule 12: Focus Appearance
      console.log('Running Focus Appearance check...');
      const focusAppearanceCheck = new FocusAppearanceCheck();
      const focusAppearanceResult = await focusAppearanceCheck.performCheck(checkConfig);
      console.log(`Focus Appearance check completed: ${focusAppearanceResult.status}`);

      // Rule 14: Target Size
      console.log('Running Target Size check...');
      const targetSizeCheck = new TargetSizeCheck();
      const targetSizeResult = await targetSizeCheck.performCheck(checkConfig);
      console.log(`Target Size check completed: ${targetSizeResult.status}`);

      console.log('✅ Part 3 automated checks completed successfully');

      // TODO: Store results in database
      // For now, just log the results
      const allResults = [
        contrastResult,
        focusVisibleResult,
        focusNotObscuredMinResult,
        focusNotObscuredEnhResult,
        focusAppearanceResult,
        targetSizeResult
      ];

      console.log('Part 3 Check Results Summary:');
      allResults.forEach(result => {
        console.log(`- ${result.ruleName}: ${result.status} (${result.score}/${result.maxScore})`);
      });

    } catch (error) {
      console.error('❌ Error running Part 3 automated checks:', error);
      throw error;
    }
  }

  /**
   * Get scan progress
   */
  async getScanProgress(scanId: string): Promise<ScanProgress> {
    // TODO: Implement database lookup for scan progress
    return {
      scanId,
      status: 'running',
      completedRules: [],
      totalRules: WCAG_AUTOMATED_RULES.length,
      progress: 0
    };
  }

  /**
   * Generate unique scan ID
   */
  private generateScanId(): string {
    return `wcag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }
      
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }
}
